/**
 * httpService.js
 *
 * 提供HTTP相关服务, 如获取指定url的html文本
 * 支持动态权限请求来处理跨域问题
 * 支持反爬虫保护、JavaScript渲染、tabs API等高级功能
 */
import { logger } from '../../util/logger.js';

class HttpService {
  constructor() {
    // 默认重试次数
    this.maxRetries = 2;
    // 重试延迟时间（毫秒）
    this.retryDelay = 1000;
    // 权限请求缓存，避免重复请求
    this.permissionCache = new Map();
    // 随机延迟范围（毫秒）
    this.randomDelayRange = [500, 2000];
    // 用户代理字符串池
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
  }

  /**
   * 设置重试配置
   * @param {number} maxRetries - 最大重试次数
   * @param {number} retryDelay - 重试延迟时间（毫秒）
   */
  setRetryConfig(maxRetries, retryDelay = 1000) {
    this.maxRetries = maxRetries;
    this.retryDelay = retryDelay;
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟时间（毫秒）
   * @returns {Promise} - 延迟Promise
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 随机延迟函数，用于反爬虫
   * @param {number} min - 最小延迟时间（毫秒）
   * @param {number} max - 最大延迟时间（毫秒）
   * @returns {Promise} - 延迟Promise
   */
  async randomDelay(min = this.randomDelayRange[0], max = this.randomDelayRange[1]) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    logger.log(`[HttpService] 随机延迟 ${delay}ms`);
    return this.delay(delay);
  }

  /**
   * 获取随机用户代理
   * @returns {string} - 随机用户代理字符串
   */
  getRandomUserAgent() {
    return this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
  }

  /**
   * 生成真实浏览器请求头
   * @param {string} url - 目标URL
   * @param {string} referer - 引用页面URL
   * @returns {Object} - 请求头对象
   */
  generateRealisticHeaders(url, referer = null) {
    const urlObj = new URL(url);
    const userAgent = this.getRandomUserAgent();

    const headers = {
      'User-Agent': userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'DNT': '1',
      'Connection': 'keep-alive',
      'Upgrade-Insecure-Requests': '1',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
      'Cache-Control': 'max-age=0'
    };

    // 添加引用页面
    if (referer) {
      headers['Referer'] = referer;
      headers['Sec-Fetch-Site'] = 'same-origin';
    }

    // 为特定网站添加特殊头部
    if (urlObj.hostname.includes('google.com')) {
      headers['Accept-Language'] = 'zh-CN,zh;q=0.9,en;q=0.8';
    } else if (urlObj.hostname.includes('ieee.org')) {
      headers['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8';
    }

    return headers;
  }

  /**
   * 从URL提取域名
   * @param {string} url - 完整URL
   * @returns {string} - 域名
   */
  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      logger.error(`[HttpService] URL解析失败: ${url}`, error);
      return null;
    }
  }

  /**
   * 检查是否有访问指定URL的权限
   * 由于插件已申请所有网站权限，直接返回true
   * @param {string} url - 目标URL
   * @returns {Promise<boolean>} - 是否有权限
   */
  async hasPermission(url) {
    try {
      // 插件已拥有所有网站权限，直接返回true
      return true;
    } catch (error) {
      logger.error(`[HttpService] 权限检查失败: ${url}`, error);
      return false;
    }
  }

  /**
   * 请求访问指定URL的权限
   * 由于插件已申请所有网站权限，直接返回true
   * @param {string} url - 目标URL
   * @returns {Promise<boolean>} - 是否成功获得权限
   */
  async requestPermission(url) {
    try {
      const domain = this.extractDomain(url);
      if (!domain) {
        logger.error(`[HttpService] 无法提取域名: ${url}`);
        return false;
      }

      // 插件已拥有所有网站权限，直接返回true
      logger.log(`[HttpService] 插件已拥有所有网站权限，跳过权限请求: ${url}`);
      
      // 更新缓存
      this.permissionCache.set(domain, true);
      
      return true;
    } catch (error) {
      logger.error(`[HttpService] 权限请求过程中发生错误: ${url}`, error);
      return false;
    }
  }

  /**
   * 根据 URL 获取网页 HTML 内容，支持重试机制和动态权限请求
   * @param {string} url - 目标网页的 URL
   * @param {Object} options - 选项配置
   * @param {number} options.retries - 当前重试次数（内部使用）
   * @param {boolean} options.skipPermissionRequest - 跳过权限请求（内部使用）
   * @param {string} options.method - 获取方法：'fetch'(默认) 或 'tabs'
   * @param {string} options.referer - 引用页面URL
   * @param {boolean} options.useRandomDelay - 是否使用随机延迟
   * @returns {Promise<string>} - 返回网页的 HTML 文本
   * @throws {Error} - 当网络请求失败或响应不 OK 时抛出错误
   */
  async getHtml(url, options = {}) {
    const {
      retries = 0,
      skipPermissionRequest = false,
      method = 'fetch',
      referer = null,
      useRandomDelay = true
    } = options;

    logger.log(`[HttpService] 开始获取网页内容: ${url} (第${retries + 1}次尝试, 方法: ${method})`);

    // 验证URL是否为绝对路径
    if (!url || typeof url !== 'string') {
      throw new Error(`[HttpService] 无效的URL: ${url}`);
    }

    // 检查URL是否是绝对路径（包含协议）
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      throw new Error(`[HttpService] URL必须是绝对路径（包含http://或https://）: ${url}`);
    }

    // 添加随机延迟以避免被检测为爬虫
    if (useRandomDelay && retries > 0) {
      await this.randomDelay();
    }

    // 根据方法选择不同的获取策略
    if (method === 'tabs') {
      return this.getHtmlViaTabs(url, { retries, skipPermissionRequest, referer });
    } else {
      return this.getHtmlViaFetch(url, { retries, skipPermissionRequest, referer });
    }
  }

  /**
   * 通过 fetch API 获取网页内容（改进版）
   * @param {string} url - 目标网页的 URL
   * @param {Object} options - 选项配置
   * @returns {Promise<string>} - 返回网页的 HTML 文本
   */
  async getHtmlViaFetch(url, options = {}) {
    const { retries = 0, skipPermissionRequest = false, referer = null } = options;

    try {
      // 生成真实浏览器请求头
      const headers = this.generateRealisticHeaders(url, referer);

      const response = await fetch(url, {
        method: 'GET',
        headers: headers,
        credentials: 'omit', // 不发送cookies，避免某些网站的限制
        cache: 'no-cache',
        redirect: 'follow'
      });

      if (!response.ok) {
        throw new Error(`[HttpService] 网络响应不 OK: ${response.status} ${response.statusText}`);
      }

      const html = await response.text();
      logger.log(`[HttpService] 成功通过fetch获取网页内容, URL: ${url}, 长度: ${html.length}`);
      return html;
    } catch (error) {
      return this.handleFetchError(error, url, options);
    }
  }

  /**
   * 处理 fetch 请求错误
   * @param {Error} error - 错误对象
   * @param {string} url - 目标URL
   * @param {Object} options - 选项配置
   * @returns {Promise<string>} - 重试或抛出错误
   */
  async handleFetchError(error, url, options) {
    const { retries = 0, skipPermissionRequest = false } = options;
    const errorMessage = error.message || error.toString();

    logger.error(`[HttpService] 获取网页内容失败: ${url} (第${retries + 1}次尝试)`, error);

    // 检查是否是跨域权限错误且还没有尝试过权限请求
    const isCorsError = errorMessage.includes('CORS') ||
                       errorMessage.includes('Access-Control-Allow-Origin') ||
                       errorMessage.includes('Cross-Origin') ||
                       errorMessage.includes('Failed to fetch') ||
                       errorMessage.includes('NetworkError') ||
                       error.name === 'TypeError';

    // 检查是否是反爬虫保护
    const isAntiBot = errorMessage.includes('403') ||
                     errorMessage.includes('Forbidden') ||
                     errorMessage.includes('Incapsula') ||
                     errorMessage.includes('Cloudflare') ||
                     errorMessage.includes('blocked');

    logger.log(`[HttpService] 错误分析 - errorMessage: "${errorMessage}", error.name: "${error.name}", isCorsError: ${isCorsError}, isAntiBot: ${isAntiBot}`);

    // 如果是反爬虫保护，尝试使用 tabs API
    if (isAntiBot && retries === 0) {
      logger.log(`[HttpService] 检测到反爬虫保护，尝试使用 tabs API: ${url}`);
      try {
        return await this.getHtmlViaTabs(url, { ...options, retries: 0 });
      } catch (tabsError) {
        logger.error(`[HttpService] tabs API 也失败了: ${url}`, tabsError);
      }
    }

    if (isCorsError && !skipPermissionRequest) {
      logger.log(`[HttpService] 检测到跨域权限错误，开始权限检查流程: ${url}`);

      const hasPermission = await this.hasPermission(url);
      logger.log(`[HttpService] 当前权限状态: ${hasPermission} for ${url}`);

      if (!hasPermission) {
        logger.warn(`[HttpService] 权限不足，但无法在后台自动请求权限（需要用户手势）: ${url}`);
        logger.warn(`[HttpService] 建议用户在popup中手动授权全局权限`);

        // 不再尝试自动权限请求，而是提供更友好的错误信息
        const domain = new URL(url).hostname;
        throw new Error(`[HttpService] 访问 ${domain} 需要额外权限。请在扩展设置中点击"请求所有权限"按钮进行授权。`);
      } else {
        logger.log(`[HttpService] 已有权限但仍然失败，可能是其他网络问题: ${url}`);
        // 即使有权限但仍然失败，可能是网络问题或服务器阻止，继续重试机制
      }
    }

    // 如果还有重试次数，则进行重试
    if (retries < this.maxRetries) {
      logger.log(`[HttpService] ${this.retryDelay}ms 后进行第${retries + 2}次重试`);
      await this.delay(this.retryDelay);
      return this.getHtml(url, { ...options, retries: retries + 1 });
    }

    // 重试次数用完，抛出最终错误
    logger.error(`[HttpService] 所有重试失败，最终获取网页内容失败: ${url}`);
    throw error;
  }
  }

  /**
   * 通过 Chrome tabs API 获取网页内容（支持JavaScript渲染）
   * @param {string} url - 目标网页的 URL
   * @param {Object} options - 选项配置
   * @returns {Promise<string>} - 返回网页的 HTML 文本
   */
  async getHtmlViaTabs(url, options = {}) {
    const { retries = 0, referer = null } = options;

    logger.log(`[HttpService] 通过 tabs API 获取网页内容: ${url}`);

    try {
      // 创建新标签页
      const tab = await chrome.tabs.create({
        url: url,
        active: false // 在后台打开
      });

      // 等待页面加载完成
      await this.waitForTabLoad(tab.id);

      // 等待额外时间让JavaScript执行
      await this.delay(2000);

      // 注入脚本获取页面内容
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          // 移除脚本标签和样式标签，只保留主要内容
          const scripts = document.querySelectorAll('script, style, noscript');
          scripts.forEach(el => el.remove());

          // 获取页面HTML
          return {
            html: document.documentElement.outerHTML,
            title: document.title,
            url: window.location.href,
            readyState: document.readyState
          };
        }
      });

      // 关闭标签页
      await chrome.tabs.remove(tab.id);

      if (results && results[0] && results[0].result) {
        const { html, title, url: finalUrl, readyState } = results[0].result;
        logger.log(`[HttpService] 成功通过tabs API获取网页内容: ${finalUrl}, 标题: ${title}, 状态: ${readyState}, 长度: ${html.length}`);
        return html;
      } else {
        throw new Error('无法从标签页获取内容');
      }

    } catch (error) {
      logger.error(`[HttpService] tabs API 获取失败: ${url}`, error);

      // 如果还有重试次数，则进行重试
      if (retries < this.maxRetries) {
        logger.log(`[HttpService] tabs API 重试: ${retries + 1}/${this.maxRetries}`);
        await this.randomDelay();
        return this.getHtmlViaTabs(url, { ...options, retries: retries + 1 });
      }

      throw error;
    }
  }

  /**
   * 等待标签页加载完成
   * @param {number} tabId - 标签页ID
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise} - 加载完成Promise
   */
  async waitForTabLoad(tabId, timeout = 30000) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        chrome.tabs.onUpdated.removeListener(listener);
        reject(new Error('标签页加载超时'));
      }, timeout);

      const listener = (updatedTabId, changeInfo, tab) => {
        if (updatedTabId === tabId && changeInfo.status === 'complete') {
          clearTimeout(timeoutId);
          chrome.tabs.onUpdated.removeListener(listener);
          resolve();
        }
      };

      chrome.tabs.onUpdated.addListener(listener);
    });
  }

  /**
   * 智能获取HTML内容（自动选择最佳方法）
   * @param {string} url - 目标网页的 URL
   * @param {Object} options - 选项配置
   * @returns {Promise<string>} - 返回网页的 HTML 文本
   */
  async getHtmlSmart(url, options = {}) {
    logger.log(`[HttpService] 智能获取网页内容: ${url}`);

    // 首先尝试 fetch 方法（更快）
    try {
      const html = await this.getHtml(url, { ...options, method: 'fetch' });

      // 检查内容质量
      if (this.isContentValid(html, url)) {
        return html;
      } else {
        logger.log(`[HttpService] fetch 获取的内容质量不佳，尝试 tabs API: ${url}`);
        throw new Error('内容质量不佳');
      }
    } catch (error) {
      logger.log(`[HttpService] fetch 方法失败，尝试 tabs API: ${url}`);

      // 如果 fetch 失败，尝试 tabs API
      return this.getHtml(url, { ...options, method: 'tabs' });
    }
  }

  /**
   * 检查获取的内容是否有效
   * @param {string} html - HTML内容
   * @param {string} url - 目标URL
   * @returns {boolean} - 内容是否有效
   */
  isContentValid(html, url) {
    if (!html || html.length < 100) {
      return false;
    }

    // 检查是否包含常见的反爬虫页面标识
    const antiBotIndicators = [
      'Incapsula incident ID',
      'Access Denied',
      'Cloudflare',
      'Please enable JavaScript',
      'Robot or human',
      'Checking your browser',
      'DDoS protection',
      'Security check'
    ];

    for (const indicator of antiBotIndicators) {
      if (html.includes(indicator)) {
        logger.log(`[HttpService] 检测到反爬虫内容: ${indicator}`);
        return false;
      }
    }

    // 检查是否包含实际内容
    const hasContent = html.includes('<title>') &&
                      html.includes('<body>') &&
                      html.length > 1000;

    return hasContent;
  }

  /**
   * 清除权限缓存
   */
  clearPermissionCache() {
    this.permissionCache.clear();
    logger.log(`[HttpService] 权限缓存已清除`);
  }

  /**
   * 获取权限缓存信息
   * @returns {Object} - 权限缓存信息
   */
  getPermissionCacheInfo() {
    const info = {};
    for (const [domain, granted] of this.permissionCache.entries()) {
      info[domain] = granted;
    }
    return info;
  }

  /**
   * 权限诊断方法
   * 由于插件已申请所有网站权限，简化诊断逻辑
   * @returns {Promise<Object>} - 诊断结果
   */
  async diagnosePermissions() {
    try {
      logger.log(`[HttpService] 开始权限诊断`);
      
      const testDomains = [
        'https://books.google.com',
        'https://scholar.google.com',
        'https://ieeexplore.ieee.org',
        'https://dl.acm.org',
        'https://arxiv.org',
        'https://cir.nii.ac.jp'
      ];

      const results = {
        timestamp: new Date().toISOString(),
        permissions: {},
        cache: this.getPermissionCacheInfo(),
        manifestPermissions: ['*://*/*'],
        optionalPermissions: [],
        recommendations: []
      };

      // 由于插件已拥有所有网站权限，所有域名都标记为已授权
      for (const domain of testDomains) {
        results.permissions[domain] = {
          hasPermission: true,
          status: 'granted'
        };
      }

      // 插件已拥有通用权限
      results.universalPermission = {
        hasPermission: true,
        status: 'granted'
      };
      
      results.recommendations.push('✅ 插件已拥有所有网站访问权限');
      results.recommendations.push('✅ 所有预定义域名都有访问权限');

      logger.log(`[HttpService] 权限诊断完成:`, results);
      return {
        success: true,
        data: results
      };

    } catch (error) {
      logger.error(`[HttpService] 权限诊断失败:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export const httpService = new HttpService();
export default httpService; 