/**
 * httpServiceExample.js
 * 
 * HttpService 使用示例和测试
 * 展示如何使用改进后的 httpService 获取网页内容
 */

import { httpService } from './httpService.js';
import { logger } from '../../util/logger.js';

/**
 * 测试不同的网页获取方法
 */
export class HttpServiceTester {
  constructor() {
    this.testUrls = [
      'https://scholar.google.com/scholar?q=machine+learning',
      'https://arxiv.org/abs/2301.00001',
      'https://ieeexplore.ieee.org/document/9999999',
      'https://psycnet.apa.org/record/1972-02073-001', // 有反爬虫保护的网站
      'https://dl.acm.org/doi/10.1145/3000000.3000001'
    ];
  }

  /**
   * 测试基本的 fetch 方法
   */
  async testBasicFetch() {
    logger.log('[HttpServiceTester] 开始测试基本 fetch 方法');
    
    for (const url of this.testUrls) {
      try {
        logger.log(`[HttpServiceTester] 测试 URL: ${url}`);
        const startTime = Date.now();
        
        const html = await httpService.getHtml(url, {
          method: 'fetch',
          useRandomDelay: false
        });
        
        const endTime = Date.now();
        const isValid = httpService.isContentValid(html, url);
        
        logger.log(`[HttpServiceTester] ✅ 成功获取: ${url}`);
        logger.log(`[HttpServiceTester] 耗时: ${endTime - startTime}ms, 长度: ${html.length}, 有效: ${isValid}`);
        
        if (!isValid) {
          logger.warn(`[HttpServiceTester] ⚠️ 内容可能被反爬虫保护: ${url}`);
        }
        
      } catch (error) {
        logger.error(`[HttpServiceTester] ❌ 获取失败: ${url}`, error);
      }
    }
  }

  /**
   * 测试 tabs API 方法
   */
  async testTabsAPI() {
    logger.log('[HttpServiceTester] 开始测试 tabs API 方法');
    
    for (const url of this.testUrls) {
      try {
        logger.log(`[HttpServiceTester] 测试 URL (tabs): ${url}`);
        const startTime = Date.now();
        
        const html = await httpService.getHtml(url, {
          method: 'tabs'
        });
        
        const endTime = Date.now();
        const isValid = httpService.isContentValid(html, url);
        
        logger.log(`[HttpServiceTester] ✅ 成功获取 (tabs): ${url}`);
        logger.log(`[HttpServiceTester] 耗时: ${endTime - startTime}ms, 长度: ${html.length}, 有效: ${isValid}`);
        
      } catch (error) {
        logger.error(`[HttpServiceTester] ❌ tabs API 获取失败: ${url}`, error);
      }
    }
  }

  /**
   * 测试智能获取方法
   */
  async testSmartFetch() {
    logger.log('[HttpServiceTester] 开始测试智能获取方法');
    
    for (const url of this.testUrls) {
      try {
        logger.log(`[HttpServiceTester] 测试 URL (smart): ${url}`);
        const startTime = Date.now();
        
        const html = await httpService.getHtmlSmart(url);
        
        const endTime = Date.now();
        const isValid = httpService.isContentValid(html, url);
        
        logger.log(`[HttpServiceTester] ✅ 智能获取成功: ${url}`);
        logger.log(`[HttpServiceTester] 耗时: ${endTime - startTime}ms, 长度: ${html.length}, 有效: ${isValid}`);
        
      } catch (error) {
        logger.error(`[HttpServiceTester] ❌ 智能获取失败: ${url}`, error);
      }
    }
  }

  /**
   * 测试反爬虫网站
   */
  async testAntiBot() {
    const antiBotUrls = [
      'https://psycnet.apa.org/record/1972-02073-001',
      'https://www.nature.com/articles/nature12373',
      'https://science.sciencemag.org/content/early/recent'
    ];

    logger.log('[HttpServiceTester] 开始测试反爬虫网站');
    
    for (const url of antiBotUrls) {
      logger.log(`[HttpServiceTester] 测试反爬虫网站: ${url}`);
      
      // 先尝试 fetch
      try {
        const fetchHtml = await httpService.getHtml(url, { method: 'fetch' });
        const fetchValid = httpService.isContentValid(fetchHtml, url);
        logger.log(`[HttpServiceTester] Fetch 结果: 长度=${fetchHtml.length}, 有效=${fetchValid}`);
      } catch (error) {
        logger.log(`[HttpServiceTester] Fetch 失败: ${error.message}`);
      }

      // 再尝试 tabs API
      try {
        const tabsHtml = await httpService.getHtml(url, { method: 'tabs' });
        const tabsValid = httpService.isContentValid(tabsHtml, url);
        logger.log(`[HttpServiceTester] Tabs 结果: 长度=${tabsHtml.length}, 有效=${tabsValid}`);
      } catch (error) {
        logger.log(`[HttpServiceTester] Tabs 失败: ${error.message}`);
      }

      // 最后尝试智能方法
      try {
        const smartHtml = await httpService.getHtmlSmart(url);
        const smartValid = httpService.isContentValid(smartHtml, url);
        logger.log(`[HttpServiceTester] Smart 结果: 长度=${smartHtml.length}, 有效=${smartValid}`);
      } catch (error) {
        logger.log(`[HttpServiceTester] Smart 失败: ${error.message}`);
      }
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    logger.log('[HttpServiceTester] 开始运行所有测试');
    
    try {
      await this.testBasicFetch();
      await this.testTabsAPI();
      await this.testSmartFetch();
      await this.testAntiBot();
      
      logger.log('[HttpServiceTester] ✅ 所有测试完成');
    } catch (error) {
      logger.error('[HttpServiceTester] ❌ 测试过程中发生错误', error);
    }
  }

  /**
   * 性能对比测试
   */
  async performanceComparison(url) {
    logger.log(`[HttpServiceTester] 开始性能对比测试: ${url}`);
    
    const methods = ['fetch', 'tabs'];
    const results = {};

    for (const method of methods) {
      const times = [];
      const validResults = [];

      for (let i = 0; i < 3; i++) {
        try {
          const startTime = Date.now();
          const html = await httpService.getHtml(url, { method });
          const endTime = Date.now();
          
          times.push(endTime - startTime);
          validResults.push(httpService.isContentValid(html, url));
          
          // 添加延迟避免被限制
          await httpService.delay(2000);
        } catch (error) {
          logger.error(`[HttpServiceTester] 性能测试失败 (${method}, 第${i+1}次):`, error);
        }
      }

      if (times.length > 0) {
        results[method] = {
          avgTime: times.reduce((a, b) => a + b, 0) / times.length,
          minTime: Math.min(...times),
          maxTime: Math.max(...times),
          successRate: times.length / 3,
          validRate: validResults.filter(v => v).length / validResults.length
        };
      }
    }

    logger.log(`[HttpServiceTester] 性能对比结果:`, results);
    return results;
  }
}

/**
 * 使用示例
 */
export const examples = {
  // 基本使用
  async basicUsage() {
    const url = 'https://scholar.google.com/scholar?q=machine+learning';
    const html = await httpService.getHtml(url);
    return html;
  },

  // 使用 tabs API
  async useTabsAPI() {
    const url = 'https://psycnet.apa.org/record/1972-02073-001';
    const html = await httpService.getHtml(url, { method: 'tabs' });
    return html;
  },

  // 智能获取
  async smartFetch() {
    const url = 'https://ieeexplore.ieee.org/document/9999999';
    const html = await httpService.getHtmlSmart(url);
    return html;
  },

  // 带引用页面的请求
  async withReferer() {
    const url = 'https://dl.acm.org/doi/10.1145/3000000.3000001';
    const referer = 'https://dl.acm.org/';
    const html = await httpService.getHtml(url, { 
      method: 'fetch', 
      referer: referer 
    });
    return html;
  }
};

export const tester = new HttpServiceTester();
