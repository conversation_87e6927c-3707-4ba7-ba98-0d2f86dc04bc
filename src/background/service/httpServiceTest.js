/**
 * httpServiceTest.js
 * 
 * HttpService 测试和使用示例
 */

import { httpService } from './httpService.js';
import { logger } from '../../util/logger.js';

/**
 * 测试改进后的 HttpService
 */
export class HttpServiceTest {
  constructor() {
    this.testUrls = [
      'https://scholar.google.com/scholar?q=machine+learning',
      'https://arxiv.org/abs/2301.00001',
      'https://psycnet.apa.org/record/1972-02073-001', // 有反爬虫保护
    ];
  }

  /**
   * 测试所有获取方法
   */
  async testAllMethods() {
    logger.log('[HttpServiceTest] 开始测试所有方法');
    
    for (const url of this.testUrls) {
      logger.log(`\n[HttpServiceTest] 测试 URL: ${url}`);
      
      // 1. 测试改进的 fetch 方法
      await this.testMethod('fetch', url);
      
      // 2. 测试 offscreen/tabs 方法
      await this.testMethod('tabs', url);
      
      // 3. 测试智能方法
      await this.testMethod('smart', url);
      
      logger.log(`[HttpServiceTest] URL ${url} 测试完成\n`);
    }
  }

  /**
   * 测试单个方法
   */
  async testMethod(method, url) {
    try {
      const startTime = Date.now();
      let html;
      
      switch (method) {
        case 'fetch':
          html = await httpService.getHtml(url, { method: 'fetch' });
          break;
        case 'tabs':
          html = await httpService.getHtml(url, { method: 'tabs' });
          break;
        case 'smart':
          html = await httpService.getHtmlSmart(url);
          break;
        default:
          throw new Error(`未知方法: ${method}`);
      }
      
      const endTime = Date.now();
      const isValid = httpService.isContentValid(html, url);
      
      logger.log(`[HttpServiceTest] ✅ ${method} 成功: 耗时 ${endTime - startTime}ms, 长度 ${html.length}, 有效 ${isValid}`);
      
      // 检查内容质量
      this.analyzeContent(html, url, method);
      
    } catch (error) {
      logger.error(`[HttpServiceTest] ❌ ${method} 失败: ${error.message}`);
    }
  }

  /**
   * 分析获取的内容质量
   */
  analyzeContent(html, url, method) {
    const analysis = {
      length: html.length,
      hasTitle: html.includes('<title>'),
      hasBody: html.includes('<body>'),
      hasContent: html.length > 1000,
      isAntiBot: false
    };

    // 检查反爬虫标识
    const antiBotIndicators = [
      'Incapsula incident ID',
      'Access Denied',
      'Cloudflare',
      'Please enable JavaScript',
      'Robot or human'
    ];

    for (const indicator of antiBotIndicators) {
      if (html.includes(indicator)) {
        analysis.isAntiBot = true;
        analysis.antiBotType = indicator;
        break;
      }
    }

    logger.log(`[HttpServiceTest] ${method} 内容分析:`, analysis);
  }

  /**
   * 对比不同方法的效果
   */
  async compareMethodsForUrl(url) {
    logger.log(`[HttpServiceTest] 对比不同方法获取: ${url}`);
    
    const results = {};
    const methods = ['fetch', 'tabs', 'smart'];
    
    for (const method of methods) {
      try {
        const startTime = Date.now();
        let html;
        
        switch (method) {
          case 'fetch':
            html = await httpService.getHtml(url, { method: 'fetch' });
            break;
          case 'tabs':
            html = await httpService.getHtml(url, { method: 'tabs' });
            break;
          case 'smart':
            html = await httpService.getHtmlSmart(url);
            break;
        }
        
        const endTime = Date.now();
        
        results[method] = {
          success: true,
          time: endTime - startTime,
          length: html.length,
          isValid: httpService.isContentValid(html, url),
          hasAntiBot: this.checkAntiBot(html)
        };
        
      } catch (error) {
        results[method] = {
          success: false,
          error: error.message
        };
      }
      
      // 添加延迟避免被限制
      await httpService.delay(2000);
    }
    
    logger.log(`[HttpServiceTest] 对比结果:`, results);
    return results;
  }

  /**
   * 检查是否有反爬虫内容
   */
  checkAntiBot(html) {
    const indicators = [
      'Incapsula incident ID',
      'Access Denied',
      'Cloudflare',
      'Please enable JavaScript',
      'Robot or human',
      'Checking your browser',
      'DDoS protection'
    ];

    for (const indicator of indicators) {
      if (html.includes(indicator)) {
        return indicator;
      }
    }
    return false;
  }

  /**
   * 测试特定的反爬虫网站
   */
  async testAntiBotSites() {
    const antiBotUrls = [
      'https://psycnet.apa.org/record/1972-02073-001',
      'https://www.nature.com/articles/nature12373',
      'https://ieeexplore.ieee.org/document/9999999'
    ];

    logger.log('[HttpServiceTest] 开始测试反爬虫网站');
    
    for (const url of antiBotUrls) {
      logger.log(`\n[HttpServiceTest] 测试反爬虫网站: ${url}`);
      await this.compareMethodsForUrl(url);
    }
  }
}

/**
 * 使用示例
 */
export const examples = {
  // 基本使用（改进的 fetch）
  async basicUsage() {
    const url = 'https://scholar.google.com/scholar?q=machine+learning';
    const html = await httpService.getHtml(url, { method: 'fetch' });
    console.log('获取的内容长度:', html.length);
    return html;
  },

  // 使用隐藏的 offscreen API
  async useOffscreenAPI() {
    const url = 'https://psycnet.apa.org/record/1972-02073-001';
    const html = await httpService.getHtml(url, { method: 'tabs' });
    console.log('通过 offscreen API 获取的内容长度:', html.length);
    return html;
  },

  // 智能获取（自动选择最佳方法）
  async smartFetch() {
    const url = 'https://ieeexplore.ieee.org/document/9999999';
    const html = await httpService.getHtmlSmart(url);
    console.log('智能获取的内容长度:', html.length);
    return html;
  },

  // 带自定义请求头
  async withCustomHeaders() {
    const url = 'https://dl.acm.org/doi/10.1145/3000000.3000001';
    const html = await httpService.getHtml(url, { 
      method: 'fetch',
      referer: 'https://dl.acm.org/',
      useRandomDelay: true
    });
    console.log('带自定义请求头获取的内容长度:', html.length);
    return html;
  }
};

// 创建测试实例
export const tester = new HttpServiceTest();

// 在 background script 中可以这样使用：
// import { tester, examples } from './httpServiceTest.js';
// 
// // 运行所有测试
// tester.testAllMethods();
// 
// // 或者运行单个示例
// examples.basicUsage().then(html => console.log('获取成功'));
